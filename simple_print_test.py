#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار طباعة مبسط - لاختبار الطباعة بدون الخادم
"""

import queue
import json
from printer_core import load_config, process_print_request

def test_simple_print():
    """اختبار طباعة مبسط مع بيانات تجريبية"""

    print("🧪 اختبار الطباعة")
    print("=" * 30)
    
    # إنشاء طابور للسجل
    log_queue = queue.Queue()
    
    # تحميل الإعدادات
    config = load_config()
    print(f"📄 الطابعة المحددة: {config.get('printer_name', 'غير محدد')}")
    
    # بيانات تجريبية
    test_data = {
        "labOrder": {
            "patient": {
                "name": "أحمد محمد الألفي",  # اسم يحتوي على حرف الألف
                "patient_age_gender": "35Y M"  # العمر والجنس
            },
            "created_at_raw": "2024-01-15T10:30:00Z"
        },
        "sampleGroups": {
            "group1": {
                "sample_code": "TEST-001",
                "name": "Serum",
                "tests": [
                    {"name": "CBC"},
                    {"name": "Glucose"},
                    {"name": "Complete Blood Count"},  # تحليل إنجليزي
                    {"name": "Creatinine"},
                    {"name": "Liver Function Test"},   # تحليل إنجليزي
                    {"name": "AST"},
                    {"name": "Cholesterol Total"}      # تحليل إنجليزي
                ]
            }
        }
    }
    
    print("📋 البيانات:")
    print(f"   المريض: {test_data['labOrder']['patient']['name']}")
    print(f"   رمز العينة: {test_data['sampleGroups']['group1']['sample_code']}")
    print()
    
    # تشغيل الطباعة
    print("🖨️  بدء الطباعة...")
    try:
        success = process_print_request(test_data, config, log_queue)

        if success:
            print("✅ نجحت الطباعة!")
        else:
            print("❌ فشلت الطباعة!")

    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    test_simple_print()
