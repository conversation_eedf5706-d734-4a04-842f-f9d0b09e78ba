#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تصميم ملصقات المختبر - إصدار محسن
4 أسطر: رقم العينة-العمر/الجنس-التاريخ، الاسم-نوع العينة، الباركود، التحاليل
"""

import os
import sys
from datetime import datetime, timezone, timedelta
from io import BytesIO
from PIL import Image, ImageDraw, ImageFont
import barcode
from barcode.writer import ImageWriter
import arabic_reshaper
from bidi.algorithm import get_display

def get_font_path(font_name):
    """الحصول على مسار الخط"""
    if getattr(sys, 'frozen', False):
        # إذا كان التطبيق مجمد (exe)
        base_path = sys._MEIPASS
    else:
        # إذا كان يعمل من الكود المصدري
        base_path = os.path.dirname(os.path.abspath(__file__))
    
    font_path = os.path.join(base_path, "fonts", font_name)
    if os.path.exists(font_path):
        return font_path
    
    # البحث في مجلد الخطوط الحالي
    current_fonts = os.path.join(os.getcwd(), "fonts", font_name)
    if os.path.exists(current_fonts):
        return current_fonts
    
    return None

def process_arabic_text(text):
    """معالجة النص العربي للعرض الصحيح"""
    if not text or text == "N/A":
        return text
    try:
        reshaped_text = arabic_reshaper.reshape(text)
        bidi_text = get_display(reshaped_text)
        return bidi_text
    except:
        return text

def create_compact_label_image(lab_order_info, sample_group, config):
    """
    إنشاء ملصق مضغوط ومنظم (50x25 مم)
    4 أسطر: رقم العينة-العمر/الجنس-التاريخ، الاسم-نوع العينة، الباركود، التحاليل
    """
    # --- الإعدادات الأساسية ---
    label_width_mm = float(config.get("label_width_mm", 50))
    label_height_mm = float(config.get("label_height_mm", 25))
    dpi = int(config.get("dpi", 300))
    padding_mm = 0.3  # مارجن صغير
    barcode_height_mm = 6.0  # باركود أصغر
    barcode_width_factor = 1.2

    def mm_to_px(mm): 
        return int(mm * dpi / 25.4)
    def pt_to_px(pt): 
        return int(pt * dpi / 72)

    label_width_px = mm_to_px(label_width_mm)
    label_height_px = mm_to_px(label_height_mm)
    padding_px = mm_to_px(padding_mm)
    barcode_height_px = mm_to_px(barcode_height_mm)
    content_width = label_width_px - padding_px * 2

    # الألوان
    text_color = config.get("text_color", "black")
    bg_color = config.get("bg_color", "white")

    # --- الخطوط (بولد وأصغر) ---
    try:
        font_patient = ImageFont.truetype(get_font_path("NotoNaskhArabic-Bold.ttf"), pt_to_px(8))  # اسم المريض عربي
        font_id = ImageFont.truetype("arialbd.ttf", pt_to_px(7))  # رقم العينة ونوع العينة بولد
        font_regular = ImageFont.truetype("arialbd.ttf", pt_to_px(6))  # العمر والتاريخ بولد
        font_tests = ImageFont.truetype("arialbd.ttf", pt_to_px(6))  # التحاليل بولد
    except Exception as e:
        print(f"خطأ في تحميل الخطوط: {e}")
        font_patient = font_id = font_regular = font_tests = ImageFont.load_default()

    # --- إنشاء الصورة ---
    image = Image.new("RGB", (label_width_px, label_height_px), bg_color)
    draw = ImageDraw.Draw(image)
    current_y = padding_px

    # --- استخراج البيانات ---
    patient_name = lab_order_info.get('patient', {}).get('name', 'N/A')
    patient_age_gender = lab_order_info.get('patient', {}).get('patient_age_gender', '')
    sample_code = sample_group.get('sample_code', 'N/A')
    sample_type = sample_group.get('name', 'N/A')
    test_names_str = ' '.join([t.get('name', '') for t in sample_group.get('tests', [])])

    try:
        dt = datetime.fromisoformat(lab_order_info.get('created_at_raw', '').replace("Z", "+00:00"))
        date_str = dt.astimezone(timezone(timedelta(hours=3))).strftime("%d/%m/%y %I:%M %p")
    except:
        date_str = "N/A"

    # --- السطر الأول: رقم العينة - العمر/الجنس - التاريخ ---
    left_x = padding_px
    center_x = label_width_px // 2
    right_x = label_width_px - padding_px

    draw.text((left_x, current_y), sample_code, font=font_id, fill=text_color, anchor="la")
    draw.text((center_x, current_y), patient_age_gender, font=font_regular, fill=text_color, anchor="ma")
    draw.text((right_x, current_y), date_str, font=font_regular, fill=text_color, anchor="ra")

    current_y += 12  # مسافة مناسبة

    # --- السطر الثاني: اسم المريض - نوع العينة (متوازيين) ---
    processed_name = process_arabic_text(patient_name)
    
    draw.text((left_x, current_y), sample_type, font=font_id, fill=text_color, anchor="la")
    draw.text((center_x, current_y), processed_name, font=font_patient, fill=text_color, anchor="ma")

    current_y += 14  # مسافة مناسبة

    # --- السطر الثالث: الباركود ---
    try:
        barcode_writer = ImageWriter()
        ean = barcode.get('code128', sample_code, writer=barcode_writer)
        barcode_fp = BytesIO()
        ean.write(barcode_fp, options={'write_text': False, 'module_height': barcode_height_mm})
        barcode_fp.seek(0)
        barcode_img = Image.open(barcode_fp)

        # تغيير حجم الباركود
        ratio = barcode_height_px / barcode_img.height
        new_width = int(barcode_img.width * ratio * barcode_width_factor)
        if new_width > content_width:
            new_width = content_width
        barcode_img = barcode_img.resize((new_width, barcode_height_px), Image.Resampling.LANCZOS)
        
        # وضع الباركود في المنتصف
        barcode_x = (label_width_px - new_width) // 2
        image.paste(barcode_img, (barcode_x, current_y))
        current_y += barcode_height_px + 6  # مسافة مناسبة
    except Exception as e:
        print(f"خطأ في إنشاء الباركود: {e}")
        current_y += 20

    # --- السطر الرابع: التحاليل (متعددة الأسطر) ---
    if test_names_str.strip():
        words = test_names_str.split()
        line = ""
        
        for word in words:
            test_line = line + " " + word if line else word
            if draw.textlength(test_line, font=font_tests) <= content_width:
                line = test_line
            else:
                if line:  # طباعة السطر الحالي
                    if current_y < label_height_px - padding_px - 8:
                        draw.text((left_x, current_y), line, font=font_tests, fill=text_color, anchor="la")
                        current_y += 9  # مسافة صغيرة بين أسطر التحاليل
                line = word
        
        # طباعة السطر الأخير
        if line and current_y < label_height_px - padding_px - 8:
            draw.text((left_x, current_y), line, font=font_tests, fill=text_color, anchor="la")

    return image

def get_available_printers():
    """
    Retrieves a list of available printers on the system.
    """
    import win32print
    
    printers = []
    try:
        printer_list = win32print.EnumPrinters(2)
        for printer in printer_list:
            printers.append(printer[2])  # printer name
    except Exception as e:
        print(f"Error getting printers: {e}")
    
    return printers
